import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock the leads page component
const mockJob = {
    warranty_request_id: 'warranty-123',
    warranty_request: {
        company: {
            name: 'Winnebago',
            support_phone: '1-800-WINNEBAGO',
            support_email: '<EMAIL>'
        },
        oem_user: {
            first_name: '<PERSON>',
            last_name: '<PERSON>',
            email: '<EMAIL>'
        }
    },
    rv_year: '2023',
    rv_make: 'Winnebago',
    rv_model: 'Vista',
    rv_type: 'Class A',
    rv_vin: '1FDEE3FL2KDA12345'
};

const mockJobWithoutOEMUser = {
    warranty_request_id: 'warranty-456',
    warranty_request: {
        company: {
            name: 'Forest River',
            support_phone: '1-800-FOREST-RIVER',
            support_email: '<EMAIL>'
        },
        oem_user: null
    }
};

const mockRegularJob = {
    warranty_request_id: null,
    warranty_request: null
};

// Simple component to test the OEM Support Card logic
function OEMSupportCardTest({ job }: { job: any }) {
    if (!job.warranty_request_id || !job.warranty_request?.company) {
        return null;
    }

    return (
        <div data-testid="oem-support-card">
            <h3>{job.warranty_request.company.name} Support</h3>
            <p>
                To order parts or for manufacturer specific questions, please contact {job.warranty_request.company.name} support:
            </p>

            {job.warranty_request.company.support_phone && (
                <div data-testid="support-phone">
                    <span>Phone:</span>
                    <div>{job.warranty_request.company.support_phone}</div>
                </div>
            )}

            {job.warranty_request.oem_user && (
                <div data-testid="oem-representative">
                    <span>Representative:</span>
                    <div>
                        {job.warranty_request.oem_user.first_name} {job.warranty_request.oem_user.last_name}
                    </div>
                </div>
            )}

            {(job.warranty_request.oem_user?.email || job.warranty_request.company.support_email) && (
                <div data-testid="support-email">
                    <span>Email:</span>
                    <div>
                        {job.warranty_request.oem_user?.email || job.warranty_request.company.support_email}
                    </div>
                </div>
            )}
        </div>
    );
}

describe('Customer Contact and OEM Support Cards', () => {
    it('should render both customer contact and OEM support cards for warranty jobs with complete data', () => {
        render(<OEMSupportCardTest job={mockJob} />);

        expect(screen.getByTestId('oem-support-card')).toBeInTheDocument();
        expect(screen.getByText('Winnebago Support')).toBeInTheDocument();
        expect(screen.getByText('To order parts or for manufacturer specific questions, please contact Winnebago support:')).toBeInTheDocument();

        // Check phone
        expect(screen.getByTestId('support-phone')).toBeInTheDocument();
        expect(screen.getByText('1-800-WINNEBAGO')).toBeInTheDocument();

        // Check representative
        expect(screen.getByTestId('oem-representative')).toBeInTheDocument();
        expect(screen.getByText('John Smith')).toBeInTheDocument();

        // Check email (should use OEM user email)
        expect(screen.getByTestId('support-email')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('should render OEM support card without OEM user data', () => {
        render(<OEMSupportCardTest job={mockJobWithoutOEMUser} />);

        expect(screen.getByTestId('oem-support-card')).toBeInTheDocument();
        expect(screen.getByText('Forest River Support')).toBeInTheDocument();

        // Check phone
        expect(screen.getByTestId('support-phone')).toBeInTheDocument();
        expect(screen.getByText('1-800-FOREST-RIVER')).toBeInTheDocument();

        // Should not have representative section
        expect(screen.queryByTestId('oem-representative')).not.toBeInTheDocument();

        // Should use company support email
        expect(screen.getByTestId('support-email')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('should not render OEM support card for regular jobs', () => {
        render(<OEMSupportCardTest job={mockRegularJob} />);

        expect(screen.queryByTestId('oem-support-card')).not.toBeInTheDocument();
    });

    it('should handle missing company data gracefully', () => {
        const jobWithoutCompany = {
            warranty_request_id: 'warranty-789',
            warranty_request: {
                company: null
            }
        };

        render(<OEMSupportCardTest job={jobWithoutCompany} />);

        expect(screen.queryByTestId('oem-support-card')).not.toBeInTheDocument();
    });
});
